#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能版本：查找CCB_v2.27.xlsx中字段的中文含义
处理字段名变体匹配问题，逐个字段搜索提高效率
"""

import pandas as pd
import openpyxl
from openpyxl import load_workbook
import re

def generate_field_variants(field_name):
    """生成字段名的所有可能变体"""
    variants = set()
    
    # 原始字段名
    variants.add(field_name)
    variants.add(field_name.upper())
    variants.add(field_name.lower())
    
    # 处理后缀 _cd 和 _ind
    base_name = field_name
    
    # 如果字段名以 _cd 或 _ind 结尾，生成去掉后缀的版本
    if field_name.endswith('_cd'):
        base_name = field_name[:-3]
        variants.add(base_name)
        variants.add(base_name.upper())
        variants.add(base_name.lower())
    elif field_name.endswith('_ind'):
        base_name = field_name[:-4]
        variants.add(base_name)
        variants.add(base_name.upper())
        variants.add(base_name.lower())
    
    # 如果字段名不以 _cd 或 _ind 结尾，生成添加后缀的版本
    if not field_name.endswith('_cd') and not field_name.endswith('_ind'):
        variants.add(field_name + '_cd')
        variants.add(field_name + '_ind')
        variants.add((field_name + '_cd').upper())
        variants.add((field_name + '_ind').upper())
    
    # 处理下划线变体
    no_underscore = field_name.replace('_', '')
    variants.add(no_underscore)
    variants.add(no_underscore.upper())
    variants.add(no_underscore.lower())
    
    # 处理空格变体
    with_spaces = field_name.replace('_', ' ')
    variants.add(with_spaces)
    variants.add(with_spaces.upper())
    variants.add(with_spaces.lower())
    
    return list(variants)

def search_field_in_all_sheets(excel_file, field_name):
    """在所有sheet中搜索单个字段"""
    print(f"\n正在搜索字段: {field_name}")
    
    # 生成字段名变体
    variants = generate_field_variants(field_name)
    print(f"  生成变体: {variants[:5]}...")  # 只显示前5个变体
    
    try:
        workbook = load_workbook(excel_file, read_only=True)
        sheet_names = workbook.sheetnames
        
        # 优先搜索可能包含字段定义的sheet
        priority_sheets = ['维表明细', '事实表明细', '辅助表明细', '代码值域', '维表目录', '事实表目录']
        other_sheets = [s for s in sheet_names if s not in priority_sheets]
        ordered_sheets = [s for s in priority_sheets if s in sheet_names] + other_sheets
        
        for sheet_name in ordered_sheets:
            print(f"    搜索sheet: {sheet_name}")
            
            try:
                # 读取sheet数据
                df = pd.read_excel(excel_file, sheet_name=sheet_name, header=None)
                df_str = df.astype(str)
                
                # 搜索所有变体
                for variant in variants:
                    result = search_variant_in_sheet(df_str, variant, field_name)
                    if result:
                        print(f"  ✓ 在sheet '{sheet_name}' 中找到匹配: {variant} -> {result}")
                        workbook.close()
                        return result
                        
            except Exception as e:
                print(f"    处理sheet {sheet_name} 时出错: {e}")
                continue
        
        workbook.close()
        print(f"  ✗ 未找到字段 {field_name}")
        return None
        
    except Exception as e:
        print(f"搜索字段 {field_name} 时出错: {e}")
        return None

def search_variant_in_sheet(df, variant, original_field):
    """在单个sheet中搜索字段变体"""
    
    # 1. 精确匹配
    for row_idx in range(len(df)):
        for col_idx in range(len(df.columns)):
            cell_value = str(df.iloc[row_idx, col_idx]).strip()
            
            # 精确匹配
            if cell_value == variant:
                meaning = find_chinese_around_smart(df, row_idx, col_idx)
                if meaning:
                    return meaning
    
    # 2. 包含匹配（字段名在单元格中）
    for row_idx in range(len(df)):
        for col_idx in range(len(df.columns)):
            cell_value = str(df.iloc[row_idx, col_idx])
            
            # 检查是否包含变体（使用单词边界）
            if re.search(r'\b' + re.escape(variant) + r'\b', cell_value, re.IGNORECASE):
                # 先检查当前单元格是否有中文
                if contains_chinese(cell_value):
                    chinese_part = extract_chinese_smart(cell_value)
                    if chinese_part and is_valid_meaning(chinese_part):
                        return chinese_part
                
                # 在周围查找中文含义
                meaning = find_chinese_around_smart(df, row_idx, col_idx)
                if meaning:
                    return meaning
    
    # 3. 模糊匹配（去掉特殊字符）
    variant_clean = re.sub(r'[_\s]', '', variant.lower())
    for row_idx in range(len(df)):
        for col_idx in range(len(df.columns)):
            cell_value = str(df.iloc[row_idx, col_idx])
            cell_clean = re.sub(r'[_\s]', '', cell_value.lower())
            
            if variant_clean in cell_clean and len(variant_clean) > 3:
                meaning = find_chinese_around_smart(df, row_idx, col_idx)
                if meaning:
                    return meaning
    
    return None

def find_chinese_around_smart(df, row_idx, col_idx):
    """智能查找周围的中文含义"""
    
    # 检查当前单元格
    current_cell = str(df.iloc[row_idx, col_idx])
    if contains_chinese(current_cell):
        chinese_part = extract_chinese_smart(current_cell)
        if chinese_part and is_valid_meaning(chinese_part):
            return chinese_part
    
    # 定义搜索优先级（先检查同行，再检查同列，最后检查周围）
    search_patterns = [
        # 同行搜索
        [(0, 1), (0, 2), (0, 3)],  # 右侧
        [(0, -1), (0, -2), (0, -3)],  # 左侧
        # 同列搜索
        [(1, 0), (2, 0), (3, 0)],  # 下方
        [(-1, 0), (-2, 0), (-3, 0)],  # 上方
        # 对角线搜索
        [(1, 1), (1, -1), (-1, 1), (-1, -1)]
    ]
    
    for pattern in search_patterns:
        for dr, dc in pattern:
            new_row = row_idx + dr
            new_col = col_idx + dc
            
            if 0 <= new_row < len(df) and 0 <= new_col < len(df.columns):
                cell_value = str(df.iloc[new_row, new_col])
                if contains_chinese(cell_value):
                    chinese_part = extract_chinese_smart(cell_value)
                    if chinese_part and is_valid_meaning(chinese_part):
                        return chinese_part
    
    return None

def contains_chinese(text):
    """检查文本是否包含中文字符"""
    return bool(re.search(r'[\u4e00-\u9fff]', text))

def extract_chinese_smart(text):
    """智能提取文本中的中文部分"""
    # 提取所有中文字符序列
    chinese_parts = re.findall(r'[\u4e00-\u9fff]+', text)
    
    if not chinese_parts:
        return None
    
    # 过滤掉太短的中文片段
    valid_parts = [part for part in chinese_parts if len(part) >= 2]
    
    if not valid_parts:
        return None
    
    # 按长度排序，优先返回较长的有意义片段
    valid_parts.sort(key=len, reverse=True)
    
    for part in valid_parts:
        if is_valid_meaning(part):
            return part
    
    # 如果没有找到有效含义，返回最长的
    return valid_parts[0] if valid_parts else None

def is_valid_meaning(chinese_text):
    """判断中文文本是否是有效的字段含义"""
    if not chinese_text or len(chinese_text) < 2:
        return False
    
    # 过滤掉一些无意义的词汇
    invalid_words = [
        '表', '维', '字段', '数据', '信息', '系统', '文档', '说明', '目录',
        '明细', '代码', '标志', '参数', '配置', '设置', '管理', '业务',
        '核心', '海外', '个人', '客户', '关系', '账户', '合约', '存款'
    ]
    
    # 如果完全匹配无效词汇，返回False
    if chinese_text in invalid_words:
        return False
    
    # 如果包含有效的业务词汇，返回True
    valid_indicators = [
        '年龄', '性别', '婚姻', '状况', '子女', '生命周期', '财务', '重要', '人士',
        '潜力', '特殊', '等级', '收入', '金额', '电话', '邮件', '地址', '企业',
        '高级', '管理', '人员', '实际', '控制', '法人', '渠道', '商机', '员工',
        '家庭', '人口', '居住', '学历', '职业', '职务', '职称', '行业', '工作',
        '单位', '性质', '新增', '建行', '星级', '代发', '工资', '信用卡', '额度',
        '网上银行', '手机银行', '电话银行', '短信银行', '微信银行', '激活',
        '龙支付', '自动', '还款', '聚财', '住房', '贷款', '经营', '消费',
        '快贷', '车贷', '保本', '理财', '非保本', '开放式', '产品', '悦生活',
        '商品', '国债', '速盈', '定投', '结售汇', '境外', '汇款', '商户',
        '大额', '存单', '学生', '全球', '支付卡', '外币', '金交所', '收费',
        '快捷', '支付', '证件', '号码', '评分', '评级'
    ]
    
    for indicator in valid_indicators:
        if indicator in chinese_text:
            return True
    
    # 如果长度大于4且不在无效词汇中，可能是有效的
    return len(chinese_text) > 4

def find_column_meanings():
    """主函数：查找所有字段的中文含义"""
    
    # 目标字段列表
    cols_name = [
        'cst_keywrd', 'target', 'call_dt', 'date2', 'current_time', 'crdt_no_ind',
        'age', 'gnd_cd', 'mar_sttn_cd', 'chl_sttn_cd', 'lcs_cd', 'blng_insid_ind',
        'pln_fnc_efct_ind', 'impt_psng_ind', 'ptnl_vip_ind', 'spclvip_ind',
        'stm_evl_cst_grd_cd', 'mo_incmam', 'cstmgr_id_ind', 'best_ctc_tel_ind',
        'best_ctc_tel7_ind', 'pref_msnd_mtdcd_ind', 'rcv_mail_adr_tpcd_ind',
        'entp_adv_mgtppl_ind', 'entp_act_ctrl_psn_ind', 'enlgps_ind',
        'cst_chnl_bsop_ind', 'empchnl_bsop_ind', 'fam_ppn_num_ind', 'rsdnc_sttn_cd',
        'eddgr_cd', 'dgr_cd', 'ocp_cd', 'post_tpcd', 'ttl_tpcd', 'idy_tpcd',
        'create_cust_first_year', 'crt_insid_ind', 'wrk_unit_char_cd', 'wrk_unit_nm_ind',
        'tyni_ccb_cst_ind', 'cust_level_ind', 'cst_star_cd', 'pyrl_cst_ind',
        'crcrd_crline', 'ebnkg_sign_ind', 'mpb_sign_ind', 'tbnk_sign_ind',
        'sms_bnk_sign_ind', 'wechat_bnk_sign_ind', 'mpb_sign_not_actvt_ind',
        'long_py_sign_ind', 'crcrd_sign_auto_repy_ind', 'ms_dep_sign_ind',
        'idv_hsln_cst_ind', 'idv_oprt_lncst_ind', 'idv_cnsmp_lncst_ind',
        'idv_othr_lncst_ind', 'qckln_cst_ind', 'psnloan_cst_ind', 'carln_cst_ind',
        'brkevn_chrtc_cst_ind', 'non_brkevn_chrtc_cst_ind', 'opn_chmtpd_cst_ind',
        'yuelf_cst_ind', 'acc_cmdty_cst_ind', 'natdbt_cst_ind', 'suying_cst_ind',
        'accgld_atim_cst_ind', 'idvexstl_cst_ind', 'idv_ovsea_rmt_cst_ind',
        'pos_mrch_sign_ind', 'bigamt_ctfofdep_cst_ind', 'stdnt_cst_ind',
        'glblpy_crd_cst_ind', 'idv_frncy_cst_ind', 'agnc_gldexg_cst_ind',
        'crnmo_smsbnk_fee_ind', 'sign_qikpay_ind', 'Cust_Org_Num_ind',
        'CCrdCstEtHPrblScorVal', 'CCrdCstEnmtHPrblSclCd', 'CCrdCstLRHPrblScorVal',
        'CCrdCstLwtRHPrblSclCd', 'CCCHlCcCvPlSc', 'CCCHlCcCvPlScCd'
    ]
    
    excel_file = 'CCB_v2.27.xlsx'
    field_meanings = {}
    
    print(f"开始逐个搜索 {len(cols_name)} 个字段...")
    
    # 逐个搜索字段
    for i, field_name in enumerate(cols_name, 1):
        print(f"\n[{i}/{len(cols_name)}] 处理字段: {field_name}")
        
        meaning = search_field_in_all_sheets(excel_file, field_name)
        if meaning:
            field_meanings[field_name] = meaning
            print(f"  ✓ 找到: {meaning}")
        else:
            print(f"  ✗ 未找到")
    
    return field_meanings

def save_results(field_meanings, output_file='字段对照表_智能版.xlsx'):
    """保存结果到Excel文件"""
    
    cols_name = [
        'cst_keywrd', 'target', 'call_dt', 'date2', 'current_time', 'crdt_no_ind',
        'age', 'gnd_cd', 'mar_sttn_cd', 'chl_sttn_cd', 'lcs_cd', 'blng_insid_ind',
        'pln_fnc_efct_ind', 'impt_psng_ind', 'ptnl_vip_ind', 'spclvip_ind',
        'stm_evl_cst_grd_cd', 'mo_incmam', 'cstmgr_id_ind', 'best_ctc_tel_ind',
        'best_ctc_tel7_ind', 'pref_msnd_mtdcd_ind', 'rcv_mail_adr_tpcd_ind',
        'entp_adv_mgtppl_ind', 'entp_act_ctrl_psn_ind', 'enlgps_ind',
        'cst_chnl_bsop_ind', 'empchnl_bsop_ind', 'fam_ppn_num_ind', 'rsdnc_sttn_cd',
        'eddgr_cd', 'dgr_cd', 'ocp_cd', 'post_tpcd', 'ttl_tpcd', 'idy_tpcd',
        'create_cust_first_year', 'crt_insid_ind', 'wrk_unit_char_cd', 'wrk_unit_nm_ind',
        'tyni_ccb_cst_ind', 'cust_level_ind', 'cst_star_cd', 'pyrl_cst_ind',
        'crcrd_crline', 'ebnkg_sign_ind', 'mpb_sign_ind', 'tbnk_sign_ind',
        'sms_bnk_sign_ind', 'wechat_bnk_sign_ind', 'mpb_sign_not_actvt_ind',
        'long_py_sign_ind', 'crcrd_sign_auto_repy_ind', 'ms_dep_sign_ind',
        'idv_hsln_cst_ind', 'idv_oprt_lncst_ind', 'idv_cnsmp_lncst_ind',
        'idv_othr_lncst_ind', 'qckln_cst_ind', 'psnloan_cst_ind', 'carln_cst_ind',
        'brkevn_chrtc_cst_ind', 'non_brkevn_chrtc_cst_ind', 'opn_chmtpd_cst_ind',
        'yuelf_cst_ind', 'acc_cmdty_cst_ind', 'natdbt_cst_ind', 'suying_cst_ind',
        'accgld_atim_cst_ind', 'idvexstl_cst_ind', 'idv_ovsea_rmt_cst_ind',
        'pos_mrch_sign_ind', 'bigamt_ctfofdep_cst_ind', 'stdnt_cst_ind',
        'glblpy_crd_cst_ind', 'idv_frncy_cst_ind', 'agnc_gldexg_cst_ind',
        'crnmo_smsbnk_fee_ind', 'sign_qikpay_ind', 'Cust_Org_Num_ind',
        'CCrdCstEtHPrblScorVal', 'CCrdCstEnmtHPrblSclCd', 'CCrdCstLRHPrblScorVal',
        'CCrdCstLwtRHPrblSclCd', 'CCCHlCcCvPlSc', 'CCCHlCcCvPlScCd'
    ]
    
    # 创建结果DataFrame
    result_data = []
    
    for field_name in cols_name:
        chinese_meaning = field_meanings.get(field_name, '未找到')
        result_data.append([field_name, chinese_meaning])
    
    # 创建DataFrame
    df_result = pd.DataFrame(result_data, columns=['字段英文名', '中文名'])
    
    # 保存到Excel
    df_result.to_excel(output_file, index=False, engine='openpyxl')
    
    print(f"\n" + "="*50)
    print(f"结果已保存到: {output_file}")
    print(f"总共处理 {len(cols_name)} 个字段")
    print(f"找到中文含义的字段: {len(field_meanings)} 个")
    print(f"未找到的字段: {len(cols_name) - len(field_meanings)} 个")
    print(f"成功率: {len(field_meanings)/len(cols_name)*100:.1f}%")
    
    # 显示找到的字段
    if field_meanings:
        print("\n找到的字段:")
        for field, meaning in field_meanings.items():
            print(f"  {field}: {meaning}")

if __name__ == "__main__":
    print("智能字段含义查找程序启动...")
    field_meanings = find_column_meanings()
    
    if field_meanings is not None:
        save_results(field_meanings)
        print("\n任务完成！")
    else:
        print("任务失败！")
