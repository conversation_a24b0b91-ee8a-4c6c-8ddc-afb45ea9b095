#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化版本：查找CCB_v2.27.xlsx中字段的中文含义
"""

import pandas as pd
import openpyxl
from openpyxl import load_workbook
import re

def find_column_meanings():
    """查找字段的中文含义"""
    
    # 目标字段列表
    cols_name = [
        'cst_keywrd', 'target', 'call_dt', 'date2', 'current_time', 'crdt_no_ind',
        'age', 'gnd_cd', 'mar_sttn_cd', 'chl_sttn_cd', 'lcs_cd', 'blng_insid_ind',
        'pln_fnc_efct_ind', 'impt_psng_ind', 'ptnl_vip_ind', 'spclvip_ind',
        'stm_evl_cst_grd_cd', 'mo_incmam', 'cstmgr_id_ind', 'best_ctc_tel_ind',
        'best_ctc_tel7_ind', 'pref_msnd_mtdcd_ind', 'rcv_mail_adr_tpcd_ind',
        'entp_adv_mgtppl_ind', 'entp_act_ctrl_psn_ind', 'enlgps_ind',
        'cst_chnl_bsop_ind', 'empchnl_bsop_ind', 'fam_ppn_num_ind', 'rsdnc_sttn_cd',
        'eddgr_cd', 'dgr_cd', 'ocp_cd', 'post_tpcd', 'ttl_tpcd', 'idy_tpcd',
        'create_cust_first_year', 'crt_insid_ind', 'wrk_unit_char_cd', 'wrk_unit_nm_ind',
        'tyni_ccb_cst_ind', 'cust_level_ind', 'cst_star_cd', 'pyrl_cst_ind',
        'crcrd_crline', 'ebnkg_sign_ind', 'mpb_sign_ind', 'tbnk_sign_ind',
        'sms_bnk_sign_ind', 'wechat_bnk_sign_ind', 'mpb_sign_not_actvt_ind',
        'long_py_sign_ind', 'crcrd_sign_auto_repy_ind', 'ms_dep_sign_ind',
        'idv_hsln_cst_ind', 'idv_oprt_lncst_ind', 'idv_cnsmp_lncst_ind',
        'idv_othr_lncst_ind', 'qckln_cst_ind', 'psnloan_cst_ind', 'carln_cst_ind',
        'brkevn_chrtc_cst_ind', 'non_brkevn_chrtc_cst_ind', 'opn_chmtpd_cst_ind',
        'yuelf_cst_ind', 'acc_cmdty_cst_ind', 'natdbt_cst_ind', 'suying_cst_ind',
        'accgld_atim_cst_ind', 'idvexstl_cst_ind', 'idv_ovsea_rmt_cst_ind',
        'pos_mrch_sign_ind', 'bigamt_ctfofdep_cst_ind', 'stdnt_cst_ind',
        'glblpy_crd_cst_ind', 'idv_frncy_cst_ind', 'agnc_gldexg_cst_ind',
        'crnmo_smsbnk_fee_ind', 'sign_qikpay_ind', 'Cust_Org_Num_ind',
        'CCrdCstEtHPrblScorVal', 'CCrdCstEnmtHPrblSclCd', 'CCrdCstLRHPrblScorVal',
        'CCrdCstLwtRHPrblSclCd', 'CCCHlCcCvPlSc', 'CCCHlCcCvPlScCd'
    ]
    
    # 结果字典
    field_meanings = {}
    
    # 读取Excel文件
    excel_file = 'CCB_v2.27.xlsx'
    
    try:
        # 获取所有sheet名称
        workbook = load_workbook(excel_file, read_only=True)
        sheet_names = workbook.sheetnames
        print(f"找到 {len(sheet_names)} 个sheet: {sheet_names}")
        
        # 优先处理可能包含字段定义的sheet
        priority_sheets = ['维表明细', '事实表明细', '辅助表明细', '代码值域']
        other_sheets = [s for s in sheet_names if s not in priority_sheets]
        ordered_sheets = priority_sheets + other_sheets
        
        # 遍历每个sheet
        for sheet_name in ordered_sheets:
            if sheet_name not in sheet_names:
                continue
                
            print(f"\n正在处理sheet: {sheet_name}")
            
            try:
                # 读取sheet数据，限制行数以提高效率
                df = pd.read_excel(excel_file, sheet_name=sheet_name, header=None, nrows=1000)
                
                # 搜索字段名和对应的中文含义
                remaining_fields = [col for col in cols_name if col not in field_meanings]
                
                if not remaining_fields:
                    print("所有字段都已找到，停止搜索")
                    break
                
                for col_name in remaining_fields:
                    found = search_field_in_dataframe(df, col_name)
                    if found:
                        field_meanings[col_name] = found
                        print(f"找到字段 {col_name}: {found}")
                        
            except Exception as e:
                print(f"处理sheet {sheet_name} 时出错: {e}")
                continue
        
        workbook.close()
        
    except Exception as e:
        print(f"读取Excel文件时出错: {e}")
        return None
    
    return field_meanings

def search_field_in_dataframe(df, field_name):
    """在DataFrame中搜索字段名及其中文含义"""
    
    # 将DataFrame转换为字符串进行搜索
    df_str = df.astype(str)
    
    # 搜索包含字段名的单元格
    for row_idx in range(len(df_str)):
        for col_idx in range(len(df_str.columns)):
            cell_value = df_str.iloc[row_idx, col_idx]
            
            # 检查是否包含目标字段名（不区分大小写）
            if field_name.lower() in cell_value.lower():
                # 尝试在同一行或相邻行找中文含义
                chinese_meaning = find_chinese_meaning_optimized(df_str, row_idx, col_idx, field_name)
                if chinese_meaning:
                    return chinese_meaning
    
    return None

def find_chinese_meaning_optimized(df, row_idx, col_idx, field_name):
    """优化版本：在指定位置附近查找中文含义"""
    
    # 检查当前单元格
    current_cell = str(df.iloc[row_idx, col_idx])
    if contains_chinese(current_cell):
        chinese_part = extract_chinese(current_cell)
        if chinese_part and len(chinese_part) > 1 and chinese_part != field_name:
            return chinese_part
    
    # 检查同一行的其他列
    for c in range(max(0, col_idx-2), min(len(df.columns), col_idx+3)):
        if c != col_idx:
            cell_value = str(df.iloc[row_idx, c])
            if contains_chinese(cell_value):
                chinese_part = extract_chinese(cell_value)
                if chinese_part and len(chinese_part) > 1:
                    return chinese_part
    
    # 检查相邻行
    for r in range(max(0, row_idx-1), min(len(df), row_idx+2)):
        if r != row_idx:
            for c in range(max(0, col_idx-1), min(len(df.columns), col_idx+2)):
                cell_value = str(df.iloc[r, c])
                if contains_chinese(cell_value):
                    chinese_part = extract_chinese(cell_value)
                    if chinese_part and len(chinese_part) > 1:
                        return chinese_part
    
    return None

def contains_chinese(text):
    """检查文本是否包含中文字符"""
    return bool(re.search(r'[\u4e00-\u9fff]', text))

def extract_chinese(text):
    """提取文本中的中文部分"""
    # 提取所有中文字符
    chinese_chars = re.findall(r'[\u4e00-\u9fff]+', text)
    if chinese_chars:
        # 返回最长的中文片段
        longest = max(chinese_chars, key=len)
        if len(longest) > 1:  # 至少2个中文字符
            return longest
    return None

def save_results(field_meanings, output_file='字段对照表.xlsx'):
    """保存结果到Excel文件"""
    
    cols_name = [
        'cst_keywrd', 'target', 'call_dt', 'date2', 'current_time', 'crdt_no_ind',
        'age', 'gnd_cd', 'mar_sttn_cd', 'chl_sttn_cd', 'lcs_cd', 'blng_insid_ind',
        'pln_fnc_efct_ind', 'impt_psng_ind', 'ptnl_vip_ind', 'spclvip_ind',
        'stm_evl_cst_grd_cd', 'mo_incmam', 'cstmgr_id_ind', 'best_ctc_tel_ind',
        'best_ctc_tel7_ind', 'pref_msnd_mtdcd_ind', 'rcv_mail_adr_tpcd_ind',
        'entp_adv_mgtppl_ind', 'entp_act_ctrl_psn_ind', 'enlgps_ind',
        'cst_chnl_bsop_ind', 'empchnl_bsop_ind', 'fam_ppn_num_ind', 'rsdnc_sttn_cd',
        'eddgr_cd', 'dgr_cd', 'ocp_cd', 'post_tpcd', 'ttl_tpcd', 'idy_tpcd',
        'create_cust_first_year', 'crt_insid_ind', 'wrk_unit_char_cd', 'wrk_unit_nm_ind',
        'tyni_ccb_cst_ind', 'cust_level_ind', 'cst_star_cd', 'pyrl_cst_ind',
        'crcrd_crline', 'ebnkg_sign_ind', 'mpb_sign_ind', 'tbnk_sign_ind',
        'sms_bnk_sign_ind', 'wechat_bnk_sign_ind', 'mpb_sign_not_actvt_ind',
        'long_py_sign_ind', 'crcrd_sign_auto_repy_ind', 'ms_dep_sign_ind',
        'idv_hsln_cst_ind', 'idv_oprt_lncst_ind', 'idv_cnsmp_lncst_ind',
        'idv_othr_lncst_ind', 'qckln_cst_ind', 'psnloan_cst_ind', 'carln_cst_ind',
        'brkevn_chrtc_cst_ind', 'non_brkevn_chrtc_cst_ind', 'opn_chmtpd_cst_ind',
        'yuelf_cst_ind', 'acc_cmdty_cst_ind', 'natdbt_cst_ind', 'suying_cst_ind',
        'accgld_atim_cst_ind', 'idvexstl_cst_ind', 'idv_ovsea_rmt_cst_ind',
        'pos_mrch_sign_ind', 'bigamt_ctfofdep_cst_ind', 'stdnt_cst_ind',
        'glblpy_crd_cst_ind', 'idv_frncy_cst_ind', 'agnc_gldexg_cst_ind',
        'crnmo_smsbnk_fee_ind', 'sign_qikpay_ind', 'Cust_Org_Num_ind',
        'CCrdCstEtHPrblScorVal', 'CCrdCstEnmtHPrblSclCd', 'CCrdCstLRHPrblScorVal',
        'CCrdCstLwtRHPrblSclCd', 'CCCHlCcCvPlSc', 'CCCHlCcCvPlScCd'
    ]
    
    # 创建结果DataFrame
    result_data = []
    
    for field_name in cols_name:
        chinese_meaning = field_meanings.get(field_name, '未找到')
        result_data.append([field_name, chinese_meaning])
    
    # 创建DataFrame
    df_result = pd.DataFrame(result_data, columns=['字段英文名', '中文名'])
    
    # 保存到Excel
    df_result.to_excel(output_file, index=False, engine='openpyxl')
    print(f"\n结果已保存到: {output_file}")
    print(f"总共处理 {len(cols_name)} 个字段")
    print(f"找到中文含义的字段: {len(field_meanings)} 个")
    print(f"未找到的字段: {len(cols_name) - len(field_meanings)} 个")
    
    # 显示找到的字段
    print("\n找到的字段:")
    for field, meaning in field_meanings.items():
        print(f"  {field}: {meaning}")

if __name__ == "__main__":
    print("开始查找字段的中文含义...")
    field_meanings = find_column_meanings()
    
    if field_meanings is not None:
        save_results(field_meanings)
        print("\n任务完成！")
    else:
        print("任务失败！")
