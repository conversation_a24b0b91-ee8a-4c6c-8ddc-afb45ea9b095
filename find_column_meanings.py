#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
查找CCB_v2.27.xlsx中字段的中文含义
"""

import pandas as pd
import openpyxl
from openpyxl import load_workbook
import re

def find_column_meanings():
    """查找字段的中文含义"""
    
    # 目标字段列表
    cols_name = [
        'cst_keywrd', 'target', 'call_dt', 'date2', 'current_time', 'crdt_no_ind',
        'age', 'gnd_cd', 'mar_sttn_cd', 'chl_sttn_cd', 'lcs_cd', 'blng_insid_ind',
        'pln_fnc_efct_ind', 'impt_psng_ind', 'ptnl_vip_ind', 'spclvip_ind',
        'stm_evl_cst_grd_cd', 'mo_incmam', 'cstmgr_id_ind', 'best_ctc_tel_ind',
        'best_ctc_tel7_ind', 'pref_msnd_mtdcd_ind', 'rcv_mail_adr_tpcd_ind',
        'entp_adv_mgtppl_ind', 'entp_act_ctrl_psn_ind', 'enlgps_ind',
        'cst_chnl_bsop_ind', 'empchnl_bsop_ind', 'fam_ppn_num_ind', 'rsdnc_sttn_cd',
        'eddgr_cd', 'dgr_cd', 'ocp_cd', 'post_tpcd', 'ttl_tpcd', 'idy_tpcd',
        'create_cust_first_year', 'crt_insid_ind', 'wrk_unit_char_cd', 'wrk_unit_nm_ind',
        'tyni_ccb_cst_ind', 'cust_level_ind', 'cst_star_cd', 'pyrl_cst_ind',
        'crcrd_crline', 'ebnkg_sign_ind', 'mpb_sign_ind', 'tbnk_sign_ind',
        'sms_bnk_sign_ind', 'wechat_bnk_sign_ind', 'mpb_sign_not_actvt_ind',
        'long_py_sign_ind', 'crcrd_sign_auto_repy_ind', 'ms_dep_sign_ind',
        'idv_hsln_cst_ind', 'idv_oprt_lncst_ind', 'idv_cnsmp_lncst_ind',
        'idv_othr_lncst_ind', 'qckln_cst_ind', 'psnloan_cst_ind', 'carln_cst_ind',
        'brkevn_chrtc_cst_ind', 'non_brkevn_chrtc_cst_ind', 'opn_chmtpd_cst_ind',
        'yuelf_cst_ind', 'acc_cmdty_cst_ind', 'natdbt_cst_ind', 'suying_cst_ind',
        'accgld_atim_cst_ind', 'idvexstl_cst_ind', 'idv_ovsea_rmt_cst_ind',
        'pos_mrch_sign_ind', 'bigamt_ctfofdep_cst_ind', 'stdnt_cst_ind',
        'glblpy_crd_cst_ind', 'idv_frncy_cst_ind', 'agnc_gldexg_cst_ind',
        'crnmo_smsbnk_fee_ind', 'sign_qikpay_ind', 'Cust_Org_Num_ind',
        'CCrdCstEtHPrblScorVal', 'CCrdCstEnmtHPrblSclCd', 'CCrdCstLRHPrblScorVal',
        'CCrdCstLwtRHPrblSclCd', 'CCCHlCcCvPlSc', 'CCCHlCcCvPlScCd'
    ]
    
    # 结果字典
    field_meanings = {}
    
    # 读取Excel文件
    excel_file = 'CCB_v2.27.xlsx'
    
    try:
        # 获取所有sheet名称
        workbook = load_workbook(excel_file, read_only=True)
        sheet_names = workbook.sheetnames
        print(f"找到 {len(sheet_names)} 个sheet: {sheet_names}")
        
        # 遍历每个sheet
        for sheet_name in sheet_names:
            print(f"\n正在处理sheet: {sheet_name}")
            
            try:
                # 读取sheet数据
                df = pd.read_excel(excel_file, sheet_name=sheet_name, header=None)
                
                # 搜索字段名和对应的中文含义
                for col_name in cols_name:
                    if col_name in field_meanings:
                        continue  # 已找到，跳过
                    
                    # 在整个sheet中搜索字段名
                    for row_idx in range(len(df)):
                        for col_idx in range(len(df.columns)):
                            cell_value = str(df.iloc[row_idx, col_idx])
                            
                            # 检查是否包含目标字段名
                            if col_name.lower() in cell_value.lower():
                                # 尝试在同一行或相邻行找中文含义
                                chinese_meaning = find_chinese_meaning(df, row_idx, col_idx, col_name)
                                if chinese_meaning:
                                    field_meanings[col_name] = chinese_meaning
                                    print(f"找到字段 {col_name}: {chinese_meaning}")
                                    break
                        if col_name in field_meanings:
                            break
                            
            except Exception as e:
                print(f"处理sheet {sheet_name} 时出错: {e}")
                continue
        
        workbook.close()
        
    except Exception as e:
        print(f"读取Excel文件时出错: {e}")
        return None
    
    return field_meanings

def find_chinese_meaning(df, row_idx, col_idx, field_name):
    """在指定位置附近查找中文含义"""
    
    # 检查当前单元格是否包含中文
    current_cell = str(df.iloc[row_idx, col_idx])
    if contains_chinese(current_cell) and field_name.lower() in current_cell.lower():
        # 提取中文部分
        chinese_part = extract_chinese(current_cell)
        if chinese_part and chinese_part != field_name:
            return chinese_part
    
    # 检查相邻单元格
    directions = [
        (0, 1),   # 右
        (0, -1),  # 左
        (1, 0),   # 下
        (-1, 0),  # 上
        (1, 1),   # 右下
        (-1, -1), # 左上
        (1, -1),  # 左下
        (-1, 1)   # 右上
    ]
    
    for dr, dc in directions:
        new_row = row_idx + dr
        new_col = col_idx + dc
        
        if 0 <= new_row < len(df) and 0 <= new_col < len(df.columns):
            cell_value = str(df.iloc[new_row, new_col])
            if contains_chinese(cell_value):
                chinese_part = extract_chinese(cell_value)
                if chinese_part and len(chinese_part) > 1:
                    return chinese_part
    
    return None

def contains_chinese(text):
    """检查文本是否包含中文字符"""
    return bool(re.search(r'[\u4e00-\u9fff]', text))

def extract_chinese(text):
    """提取文本中的中文部分"""
    chinese_chars = re.findall(r'[\u4e00-\u9fff]+', text)
    if chinese_chars:
        return ''.join(chinese_chars)
    return None

def save_results(field_meanings, output_file='字段对照表.xlsx'):
    """保存结果到Excel文件"""
    
    # 创建结果DataFrame
    result_data = []
    
    # 添加找到的字段
    for field_name, chinese_meaning in field_meanings.items():
        result_data.append([field_name, chinese_meaning])
    
    # 添加未找到的字段
    cols_name = [
        'cst_keywrd', 'target', 'call_dt', 'date2', 'current_time', 'crdt_no_ind',
        'age', 'gnd_cd', 'mar_sttn_cd', 'chl_sttn_cd', 'lcs_cd', 'blng_insid_ind',
        'pln_fnc_efct_ind', 'impt_psng_ind', 'ptnl_vip_ind', 'spclvip_ind',
        'stm_evl_cst_grd_cd', 'mo_incmam', 'cstmgr_id_ind', 'best_ctc_tel_ind',
        'best_ctc_tel7_ind', 'pref_msnd_mtdcd_ind', 'rcv_mail_adr_tpcd_ind',
        'entp_adv_mgtppl_ind', 'entp_act_ctrl_psn_ind', 'enlgps_ind',
        'cst_chnl_bsop_ind', 'empchnl_bsop_ind', 'fam_ppn_num_ind', 'rsdnc_sttn_cd',
        'eddgr_cd', 'dgr_cd', 'ocp_cd', 'post_tpcd', 'ttl_tpcd', 'idy_tpcd',
        'create_cust_first_year', 'crt_insid_ind', 'wrk_unit_char_cd', 'wrk_unit_nm_ind',
        'tyni_ccb_cst_ind', 'cust_level_ind', 'cst_star_cd', 'pyrl_cst_ind',
        'crcrd_crline', 'ebnkg_sign_ind', 'mpb_sign_ind', 'tbnk_sign_ind',
        'sms_bnk_sign_ind', 'wechat_bnk_sign_ind', 'mpb_sign_not_actvt_ind',
        'long_py_sign_ind', 'crcrd_sign_auto_repy_ind', 'ms_dep_sign_ind',
        'idv_hsln_cst_ind', 'idv_oprt_lncst_ind', 'idv_cnsmp_lncst_ind',
        'idv_othr_lncst_ind', 'qckln_cst_ind', 'psnloan_cst_ind', 'carln_cst_ind',
        'brkevn_chrtc_cst_ind', 'non_brkevn_chrtc_cst_ind', 'opn_chmtpd_cst_ind',
        'yuelf_cst_ind', 'acc_cmdty_cst_ind', 'natdbt_cst_ind', 'suying_cst_ind',
        'accgld_atim_cst_ind', 'idvexstl_cst_ind', 'idv_ovsea_rmt_cst_ind',
        'pos_mrch_sign_ind', 'bigamt_ctfofdep_cst_ind', 'stdnt_cst_ind',
        'glblpy_crd_cst_ind', 'idv_frncy_cst_ind', 'agnc_gldexg_cst_ind',
        'crnmo_smsbnk_fee_ind', 'sign_qikpay_ind', 'Cust_Org_Num_ind',
        'CCrdCstEtHPrblScorVal', 'CCrdCstEnmtHPrblSclCd', 'CCrdCstLRHPrblScorVal',
        'CCrdCstLwtRHPrblSclCd', 'CCCHlCcCvPlSc', 'CCCHlCcCvPlScCd'
    ]
    
    for field_name in cols_name:
        if field_name not in field_meanings:
            result_data.append([field_name, '未找到'])
    
    # 创建DataFrame
    df_result = pd.DataFrame(result_data, columns=['字段英文名', '中文名'])
    
    # 保存到Excel
    df_result.to_excel(output_file, index=False, engine='openpyxl')
    print(f"\n结果已保存到: {output_file}")
    print(f"总共处理 {len(cols_name)} 个字段")
    print(f"找到中文含义的字段: {len(field_meanings)} 个")
    print(f"未找到的字段: {len(cols_name) - len(field_meanings)} 个")

if __name__ == "__main__":
    print("开始查找字段的中文含义...")
    field_meanings = find_column_meanings()
    
    if field_meanings is not None:
        save_results(field_meanings)
        print("\n任务完成！")
    else:
        print("任务失败！")
