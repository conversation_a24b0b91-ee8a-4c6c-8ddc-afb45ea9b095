#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
改进版本：查找CCB_v2.27.xlsx中字段的中文含义
使用更精确的匹配策略
"""

import pandas as pd
import openpyxl
from openpyxl import load_workbook
import re

def find_column_meanings():
    """查找字段的中文含义"""
    
    # 目标字段列表
    cols_name = [
        'cst_keywrd', 'target', 'call_dt', 'date2', 'current_time', 'crdt_no_ind',
        'age', 'gnd_cd', 'mar_sttn_cd', 'chl_sttn_cd', 'lcs_cd', 'blng_insid_ind',
        'pln_fnc_efct_ind', 'impt_psng_ind', 'ptnl_vip_ind', 'spclvip_ind',
        'stm_evl_cst_grd_cd', 'mo_incmam', 'cstmgr_id_ind', 'best_ctc_tel_ind',
        'best_ctc_tel7_ind', 'pref_msnd_mtdcd_ind', 'rcv_mail_adr_tpcd_ind',
        'entp_adv_mgtppl_ind', 'entp_act_ctrl_psn_ind', 'enlgps_ind',
        'cst_chnl_bsop_ind', 'empchnl_bsop_ind', 'fam_ppn_num_ind', 'rsdnc_sttn_cd',
        'eddgr_cd', 'dgr_cd', 'ocp_cd', 'post_tpcd', 'ttl_tpcd', 'idy_tpcd',
        'create_cust_first_year', 'crt_insid_ind', 'wrk_unit_char_cd', 'wrk_unit_nm_ind',
        'tyni_ccb_cst_ind', 'cust_level_ind', 'cst_star_cd', 'pyrl_cst_ind',
        'crcrd_crline', 'ebnkg_sign_ind', 'mpb_sign_ind', 'tbnk_sign_ind',
        'sms_bnk_sign_ind', 'wechat_bnk_sign_ind', 'mpb_sign_not_actvt_ind',
        'long_py_sign_ind', 'crcrd_sign_auto_repy_ind', 'ms_dep_sign_ind',
        'idv_hsln_cst_ind', 'idv_oprt_lncst_ind', 'idv_cnsmp_lncst_ind',
        'idv_othr_lncst_ind', 'qckln_cst_ind', 'psnloan_cst_ind', 'carln_cst_ind',
        'brkevn_chrtc_cst_ind', 'non_brkevn_chrtc_cst_ind', 'opn_chmtpd_cst_ind',
        'yuelf_cst_ind', 'acc_cmdty_cst_ind', 'natdbt_cst_ind', 'suying_cst_ind',
        'accgld_atim_cst_ind', 'idvexstl_cst_ind', 'idv_ovsea_rmt_cst_ind',
        'pos_mrch_sign_ind', 'bigamt_ctfofdep_cst_ind', 'stdnt_cst_ind',
        'glblpy_crd_cst_ind', 'idv_frncy_cst_ind', 'agnc_gldexg_cst_ind',
        'crnmo_smsbnk_fee_ind', 'sign_qikpay_ind', 'Cust_Org_Num_ind',
        'CCrdCstEtHPrblScorVal', 'CCrdCstEnmtHPrblSclCd', 'CCrdCstLRHPrblScorVal',
        'CCrdCstLwtRHPrblSclCd', 'CCCHlCcCvPlSc', 'CCCHlCcCvPlScCd'
    ]
    
    # 结果字典
    field_meanings = {}
    
    # 读取Excel文件
    excel_file = 'CCB_v2.27.xlsx'
    
    try:
        # 获取所有sheet名称
        workbook = load_workbook(excel_file, read_only=True)
        sheet_names = workbook.sheetnames
        print(f"找到 {len(sheet_names)} 个sheet: {sheet_names}")
        
        # 遍历每个sheet
        for sheet_name in sheet_names:
            print(f"\n正在处理sheet: {sheet_name}")
            
            try:
                # 读取sheet数据
                df = pd.read_excel(excel_file, sheet_name=sheet_name, header=None)
                
                # 将DataFrame转换为字符串
                df_str = df.astype(str)
                
                # 搜索字段名和对应的中文含义
                for col_name in cols_name:
                    if col_name in field_meanings:
                        continue  # 已找到，跳过
                    
                    # 搜索字段名
                    found_meaning = search_field_comprehensive(df_str, col_name)
                    if found_meaning:
                        field_meanings[col_name] = found_meaning
                        print(f"找到字段 {col_name}: {found_meaning}")
                        
            except Exception as e:
                print(f"处理sheet {sheet_name} 时出错: {e}")
                continue
        
        workbook.close()
        
    except Exception as e:
        print(f"读取Excel文件时出错: {e}")
        return None
    
    return field_meanings

def search_field_comprehensive(df, field_name):
    """全面搜索字段名及其中文含义"""
    
    # 1. 精确匹配字段名
    for row_idx in range(len(df)):
        for col_idx in range(len(df.columns)):
            cell_value = str(df.iloc[row_idx, col_idx])
            
            # 精确匹配字段名
            if cell_value.strip().lower() == field_name.lower():
                # 在周围查找中文含义
                meaning = find_chinese_around(df, row_idx, col_idx)
                if meaning:
                    return meaning
    
    # 2. 包含匹配
    for row_idx in range(len(df)):
        for col_idx in range(len(df.columns)):
            cell_value = str(df.iloc[row_idx, col_idx])
            
            # 检查是否包含字段名
            if field_name.lower() in cell_value.lower():
                # 检查当前单元格是否有中文
                if contains_chinese(cell_value):
                    chinese_part = extract_chinese_clean(cell_value)
                    if chinese_part:
                        return chinese_part
                
                # 在周围查找中文含义
                meaning = find_chinese_around(df, row_idx, col_idx)
                if meaning:
                    return meaning
    
    # 3. 模糊匹配（去掉下划线等）
    field_clean = field_name.replace('_', '').lower()
    for row_idx in range(len(df)):
        for col_idx in range(len(df.columns)):
            cell_value = str(df.iloc[row_idx, col_idx])
            cell_clean = cell_value.replace('_', '').replace(' ', '').lower()
            
            if field_clean in cell_clean:
                meaning = find_chinese_around(df, row_idx, col_idx)
                if meaning:
                    return meaning
    
    return None

def find_chinese_around(df, row_idx, col_idx):
    """在指定位置周围查找中文含义"""
    
    # 检查当前单元格
    current_cell = str(df.iloc[row_idx, col_idx])
    if contains_chinese(current_cell):
        chinese_part = extract_chinese_clean(current_cell)
        if chinese_part:
            return chinese_part
    
    # 定义搜索范围
    search_range = [
        (0, 1), (0, -1), (1, 0), (-1, 0),  # 上下左右
        (0, 2), (0, -2), (2, 0), (-2, 0),  # 扩大范围
        (1, 1), (1, -1), (-1, 1), (-1, -1)  # 对角线
    ]
    
    for dr, dc in search_range:
        new_row = row_idx + dr
        new_col = col_idx + dc
        
        if 0 <= new_row < len(df) and 0 <= new_col < len(df.columns):
            cell_value = str(df.iloc[new_row, new_col])
            if contains_chinese(cell_value):
                chinese_part = extract_chinese_clean(cell_value)
                if chinese_part:
                    return chinese_part
    
    return None

def contains_chinese(text):
    """检查文本是否包含中文字符"""
    return bool(re.search(r'[\u4e00-\u9fff]', text))

def extract_chinese_clean(text):
    """提取并清理文本中的中文部分"""
    # 提取所有中文字符序列
    chinese_parts = re.findall(r'[\u4e00-\u9fff]+', text)
    
    if not chinese_parts:
        return None
    
    # 过滤掉太短的中文片段
    valid_parts = [part for part in chinese_parts if len(part) >= 2]
    
    if not valid_parts:
        return None
    
    # 返回最长的中文片段
    longest_part = max(valid_parts, key=len)
    
    # 过滤掉一些常见的无意义词汇
    exclude_words = ['表', '维', '代码', '标志', '字段', '数据', '信息', '系统']
    if longest_part in exclude_words:
        # 如果最长的是无意义词汇，尝试返回其他的
        other_parts = [part for part in valid_parts if part != longest_part and part not in exclude_words]
        if other_parts:
            return max(other_parts, key=len)
    
    return longest_part

def save_results(field_meanings, output_file='字段对照表_v2.xlsx'):
    """保存结果到Excel文件"""
    
    cols_name = [
        'cst_keywrd', 'target', 'call_dt', 'date2', 'current_time', 'crdt_no_ind',
        'age', 'gnd_cd', 'mar_sttn_cd', 'chl_sttn_cd', 'lcs_cd', 'blng_insid_ind',
        'pln_fnc_efct_ind', 'impt_psng_ind', 'ptnl_vip_ind', 'spclvip_ind',
        'stm_evl_cst_grd_cd', 'mo_incmam', 'cstmgr_id_ind', 'best_ctc_tel_ind',
        'best_ctc_tel7_ind', 'pref_msnd_mtdcd_ind', 'rcv_mail_adr_tpcd_ind',
        'entp_adv_mgtppl_ind', 'entp_act_ctrl_psn_ind', 'enlgps_ind',
        'cst_chnl_bsop_ind', 'empchnl_bsop_ind', 'fam_ppn_num_ind', 'rsdnc_sttn_cd',
        'eddgr_cd', 'dgr_cd', 'ocp_cd', 'post_tpcd', 'ttl_tpcd', 'idy_tpcd',
        'create_cust_first_year', 'crt_insid_ind', 'wrk_unit_char_cd', 'wrk_unit_nm_ind',
        'tyni_ccb_cst_ind', 'cust_level_ind', 'cst_star_cd', 'pyrl_cst_ind',
        'crcrd_crline', 'ebnkg_sign_ind', 'mpb_sign_ind', 'tbnk_sign_ind',
        'sms_bnk_sign_ind', 'wechat_bnk_sign_ind', 'mpb_sign_not_actvt_ind',
        'long_py_sign_ind', 'crcrd_sign_auto_repy_ind', 'ms_dep_sign_ind',
        'idv_hsln_cst_ind', 'idv_oprt_lncst_ind', 'idv_cnsmp_lncst_ind',
        'idv_othr_lncst_ind', 'qckln_cst_ind', 'psnloan_cst_ind', 'carln_cst_ind',
        'brkevn_chrtc_cst_ind', 'non_brkevn_chrtc_cst_ind', 'opn_chmtpd_cst_ind',
        'yuelf_cst_ind', 'acc_cmdty_cst_ind', 'natdbt_cst_ind', 'suying_cst_ind',
        'accgld_atim_cst_ind', 'idvexstl_cst_ind', 'idv_ovsea_rmt_cst_ind',
        'pos_mrch_sign_ind', 'bigamt_ctfofdep_cst_ind', 'stdnt_cst_ind',
        'glblpy_crd_cst_ind', 'idv_frncy_cst_ind', 'agnc_gldexg_cst_ind',
        'crnmo_smsbnk_fee_ind', 'sign_qikpay_ind', 'Cust_Org_Num_ind',
        'CCrdCstEtHPrblScorVal', 'CCrdCstEnmtHPrblSclCd', 'CCrdCstLRHPrblScorVal',
        'CCrdCstLwtRHPrblSclCd', 'CCCHlCcCvPlSc', 'CCCHlCcCvPlScCd'
    ]
    
    # 创建结果DataFrame
    result_data = []
    
    for field_name in cols_name:
        chinese_meaning = field_meanings.get(field_name, '未找到')
        result_data.append([field_name, chinese_meaning])
    
    # 创建DataFrame
    df_result = pd.DataFrame(result_data, columns=['字段英文名', '中文名'])
    
    # 保存到Excel
    df_result.to_excel(output_file, index=False, engine='openpyxl')
    print(f"\n结果已保存到: {output_file}")
    print(f"总共处理 {len(cols_name)} 个字段")
    print(f"找到中文含义的字段: {len(field_meanings)} 个")
    print(f"未找到的字段: {len(cols_name) - len(field_meanings)} 个")
    
    # 显示找到的字段
    print("\n找到的字段:")
    for field, meaning in field_meanings.items():
        print(f"  {field}: {meaning}")

if __name__ == "__main__":
    print("开始查找字段的中文含义...")
    field_meanings = find_column_meanings()
    
    if field_meanings is not None:
        save_results(field_meanings)
        print("\n任务完成！")
    else:
        print("任务失败！")
