#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终优化版本：查找CCB_v2.27.xlsx中字段的中文含义
在"属性英文名"列（第2列）中查找字段，获取对应的"属性中文名"列（第3列）的内容
"""

import pandas as pd
import openpyxl
from openpyxl import load_workbook

def search_field_in_sheet(df, field_name, sheet_name):
    """在单个sheet中查找字段，只返回第一个匹配的中文名"""

    # 检查是否有足够的列
    if len(df.columns) < 4:
        print(f"    跳过：列数不足（{len(df.columns)}列）")
        return None

    # 检查表头是否正确（第2列应该是属性英文名，第3列应该是属性中文名）
    if len(df) > 0:
        col2_header = str(df.iloc[0, 2]).strip()
        col3_header = str(df.iloc[0, 3]).strip()
        print(f"    表头检查: 第2列='{col2_header}', 第3列='{col3_header}'")

        if '属性英文名' not in col2_header or '属性中文名' not in col3_header:
            print(f"    跳过：表头不匹配")
            return None

    # 从第3行开始搜索（跳过表头和说明行）
    # 先进行精确匹配
    for row_idx in range(3, len(df)):
        try:
            # 获取属性英文名（第2列）
            english_name = str(df.iloc[row_idx, 2]).strip()

            # 跳过空值和无效值
            if english_name == 'nan' or english_name == '' or len(english_name) < 2:
                continue

            # 不区分大小写精确比较
            if english_name.lower() == field_name.lower():
                # 获取对应的中文名（第3列）
                chinese_name = str(df.iloc[row_idx, 3]).strip()
                if chinese_name and chinese_name != 'nan' and len(chinese_name) > 0:
                    print(f"    ✓ 精确匹配: '{english_name}' -> '{chinese_name}'")
                    return chinese_name  # 立即返回第一个匹配的结果
        except Exception as e:
            continue

    # 如果精确匹配失败，尝试包含匹配，同样只返回第一个匹配的结果
    for row_idx in range(3, len(df)):
        try:
            english_name = str(df.iloc[row_idx, 2]).strip()

            if english_name == 'nan' or english_name == '' or len(english_name) < 2:
                continue

            # 检查是否包含字段名（不区分大小写）
            if (field_name.lower() in english_name.lower() and len(field_name) > 3) or \
               (english_name.lower() in field_name.lower() and len(english_name) > 3):
                chinese_name = str(df.iloc[row_idx, 3]).strip()
                if chinese_name and chinese_name != 'nan' and len(chinese_name) > 0:
                    print(f"    ✓ 包含匹配: '{english_name}' -> '{chinese_name}'")
                    return chinese_name  # 立即返回第一个匹配的结果
        except Exception as e:
            continue

    return None

def search_field_in_all_sheets(excel_file, field_name):
    """只在维表明细和事实表明细两个sheet中搜索单个字段"""
    print(f"\n正在搜索字段: '{field_name}'")

    try:
        workbook = load_workbook(excel_file, read_only=True)

        # 严格按要求只搜索这两个sheet
        target_sheets = ['维表明细', '事实表明细']

        for sheet_name in target_sheets:
            if sheet_name not in workbook.sheetnames:
                print(f"  警告: sheet '{sheet_name}' 不存在")
                continue

            print(f"  搜索sheet: {sheet_name}")

            try:
                # 读取sheet数据
                df = pd.read_excel(excel_file, sheet_name=sheet_name, header=None)

                # 在当前sheet中查找，找到第一个匹配就立即返回
                result = search_field_in_sheet(df, field_name, sheet_name)
                if result:
                    print(f"  ✓ 在sheet '{sheet_name}' 中找到: '{field_name}' -> '{result}'")
                    workbook.close()
                    return result  # 找到第一个匹配就立即返回

            except Exception as e:
                print(f"    处理sheet {sheet_name} 时出错: {e}")
                continue

        workbook.close()
        print(f"  ✗ 未找到字段 '{field_name}'")
        return None

    except Exception as e:
        print(f"搜索字段 {field_name} 时出错: {e}")
        return None

def find_column_meanings():
    """主函数：查找所有字段的中文含义"""
    
    # 目标字段列表
    cols_name = [
        'cst_keywrd', 'target', 'call_dt', 'date2', 'current_time', 'crdt_no_ind',
        'age', 'gnd_cd', 'mar_sttn_cd', 'chl_sttn_cd', 'lcs_cd', 'blng_insid_ind',
        'pln_fnc_efct_ind', 'impt_psng_ind', 'ptnl_vip_ind', 'spclvip_ind',
        'stm_evl_cst_grd_cd', 'mo_incmam', 'cstmgr_id_ind', 'best_ctc_tel_ind',
        'best_ctc_tel7_ind', 'pref_msnd_mtdcd_ind', 'rcv_mail_adr_tpcd_ind',
        'entp_adv_mgtppl_ind', 'entp_act_ctrl_psn_ind', 'enlgps_ind',
        'cst_chnl_bsop_ind', 'empchnl_bsop_ind', 'fam_ppn_num_ind', 'rsdnc_sttn_cd',
        'eddgr_cd', 'dgr_cd', 'ocp_cd', 'post_tpcd', 'ttl_tpcd', 'idy_tpcd',
        'create_cust_first_year', 'crt_insid_ind', 'wrk_unit_char_cd', 'wrk_unit_nm_ind',
        'tyni_ccb_cst_ind', 'cust_level_ind', 'cst_star_cd', 'pyrl_cst_ind',
        'crcrd_crline', 'ebnkg_sign_ind', 'mpb_sign_ind', 'tbnk_sign_ind',
        'sms_bnk_sign_ind', 'wechat_bnk_sign_ind', 'mpb_sign_not_actvt_ind',
        'long_py_sign_ind', 'crcrd_sign_auto_repy_ind', 'ms_dep_sign_ind',
        'idv_hsln_cst_ind', 'idv_oprt_lncst_ind', 'idv_cnsmp_lncst_ind',
        'idv_othr_lncst_ind', 'qckln_cst_ind', 'psnloan_cst_ind', 'carln_cst_ind',
        'brkevn_chrtc_cst_ind', 'non_brkevn_chrtc_cst_ind', 'opn_chmtpd_cst_ind',
        'yuelf_cst_ind', 'acc_cmdty_cst_ind', 'natdbt_cst_ind', 'suying_cst_ind',
        'accgld_atim_cst_ind', 'idvexstl_cst_ind', 'idv_ovsea_rmt_cst_ind',
        'pos_mrch_sign_ind', 'bigamt_ctfofdep_cst_ind', 'stdnt_cst_ind',
        'glblpy_crd_cst_ind', 'idv_frncy_cst_ind', 'agnc_gldexg_cst_ind',
        'crnmo_smsbnk_fee_ind', 'sign_qikpay_ind', 'Cust_Org_Num_ind',
        'CCrdCstEtHPrblScorVal', 'CCrdCstEnmtHPrblSclCd', 'CCrdCstLRHPrblScorVal',
        'CCrdCstLwtRHPrblSclCd', 'CCCHlCcCvPlSc', 'CCCHlCcCvPlScCd'
    ]
    
    excel_file = 'CCB_v2.27.xlsx'
    field_meanings = {}
    
    print(f"开始逐个搜索 {len(cols_name)} 个字段...")
    print("搜索策略：在'属性英文名'列（第2列）中查找字段，获取对应的'属性中文名'列（第3列）")
    print("注意：字段名称不区分大小写")
    print("="*60)
    
    # 逐个搜索字段
    for i, field_name in enumerate(cols_name, 1):
        print(f"\n[{i}/{len(cols_name)}] 处理字段: '{field_name}'")
        
        meaning = search_field_in_all_sheets(excel_file, field_name)
        if meaning:
            field_meanings[field_name] = meaning
            print(f"  ✓ 成功找到: '{field_name}' -> '{meaning}'")
        else:
            print(f"  ✗ 未找到字段: '{field_name}'")
    
    return field_meanings

def save_results(field_meanings, output_file='字段对照表_最终版.xlsx'):
    """保存结果到Excel文件"""
    
    cols_name = [
        'cst_keywrd', 'target', 'call_dt', 'date2', 'current_time', 'crdt_no_ind',
        'age', 'gnd_cd', 'mar_sttn_cd', 'chl_sttn_cd', 'lcs_cd', 'blng_insid_ind',
        'pln_fnc_efct_ind', 'impt_psng_ind', 'ptnl_vip_ind', 'spclvip_ind',
        'stm_evl_cst_grd_cd', 'mo_incmam', 'cstmgr_id_ind', 'best_ctc_tel_ind',
        'best_ctc_tel7_ind', 'pref_msnd_mtdcd_ind', 'rcv_mail_adr_tpcd_ind',
        'entp_adv_mgtppl_ind', 'entp_act_ctrl_psn_ind', 'enlgps_ind',
        'cst_chnl_bsop_ind', 'empchnl_bsop_ind', 'fam_ppn_num_ind', 'rsdnc_sttn_cd',
        'eddgr_cd', 'dgr_cd', 'ocp_cd', 'post_tpcd', 'ttl_tpcd', 'idy_tpcd',
        'create_cust_first_year', 'crt_insid_ind', 'wrk_unit_char_cd', 'wrk_unit_nm_ind',
        'tyni_ccb_cst_ind', 'cust_level_ind', 'cst_star_cd', 'pyrl_cst_ind',
        'crcrd_crline', 'ebnkg_sign_ind', 'mpb_sign_ind', 'tbnk_sign_ind',
        'sms_bnk_sign_ind', 'wechat_bnk_sign_ind', 'mpb_sign_not_actvt_ind',
        'long_py_sign_ind', 'crcrd_sign_auto_repy_ind', 'ms_dep_sign_ind',
        'idv_hsln_cst_ind', 'idv_oprt_lncst_ind', 'idv_cnsmp_lncst_ind',
        'idv_othr_lncst_ind', 'qckln_cst_ind', 'psnloan_cst_ind', 'carln_cst_ind',
        'brkevn_chrtc_cst_ind', 'non_brkevn_chrtc_cst_ind', 'opn_chmtpd_cst_ind',
        'yuelf_cst_ind', 'acc_cmdty_cst_ind', 'natdbt_cst_ind', 'suying_cst_ind',
        'accgld_atim_cst_ind', 'idvexstl_cst_ind', 'idv_ovsea_rmt_cst_ind',
        'pos_mrch_sign_ind', 'bigamt_ctfofdep_cst_ind', 'stdnt_cst_ind',
        'glblpy_crd_cst_ind', 'idv_frncy_cst_ind', 'agnc_gldexg_cst_ind',
        'crnmo_smsbnk_fee_ind', 'sign_qikpay_ind', 'Cust_Org_Num_ind',
        'CCrdCstEtHPrblScorVal', 'CCrdCstEnmtHPrblSclCd', 'CCrdCstLRHPrblScorVal',
        'CCrdCstLwtRHPrblSclCd', 'CCCHlCcCvPlSc', 'CCCHlCcCvPlScCd'
    ]
    
    # 创建结果DataFrame
    result_data = []
    
    for field_name in cols_name:
        # 将字段名转换为大写（按照您的要求）
        field_name_upper = field_name.upper()
        chinese_meaning = field_meanings.get(field_name, '未找到')
        result_data.append([field_name_upper, chinese_meaning])
    
    # 创建DataFrame
    df_result = pd.DataFrame(result_data, columns=['字段英文名', '中文名'])
    
    # 保存到Excel
    df_result.to_excel(output_file, index=False, engine='openpyxl')
    
    print(f"\n" + "="*60)
    print(f"结果已保存到: {output_file}")
    print(f"总共处理 {len(cols_name)} 个字段")
    print(f"找到中文含义的字段: {len(field_meanings)} 个")
    print(f"未找到的字段: {len(cols_name) - len(field_meanings)} 个")
    print(f"成功率: {len(field_meanings)/len(cols_name)*100:.1f}%")
    
    # 显示找到的字段
    if field_meanings:
        print(f"\n找到的字段列表:")
        for field, meaning in field_meanings.items():
            print(f"  {field.upper()}: {meaning}")
    
    # 显示未找到的字段
    not_found = [field for field in cols_name if field not in field_meanings]
    if not_found:
        print(f"\n未找到的字段列表:")
        for field in not_found:
            print(f"  {field.upper()}")

if __name__ == "__main__":
    print("最终优化版字段含义查找程序启动...")
    print("示例：查找 'cst_keywrd' -> 输出 'CST_KEYWRD' 和 '客户关键字'")
    print("程序将显示每次查找的详细过程")
    
    field_meanings = find_column_meanings()
    
    if field_meanings is not None:
        save_results(field_meanings)
        print("\n任务完成！")
    else:
        print("任务失败！")
